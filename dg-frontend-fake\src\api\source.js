import { get, post } from '@/http/request'
import { source } from '@/api/PATH'

// 关系/目标数据源
// src：关系，tag：目标
export function getList(formData, tag) {
  if (tag === 'src') {
    return post(source.relationList, { data: JSON.stringify(formData) })
  } else if (tag === 'tag') {
    return post(source.targetList, { data: JSON.stringify(formData) })
  }
}

export function getTableList(formData) {
  return post(source.tableList, { data: JSON.stringify(formData) })
}

// export const testSourceController = new AbortController()
export function testSource(name) {
  return post(source.test, { data: JSON.stringify({ name }) }, null, null, { timeout: 3000 })
}

export function addSource(formData, tag) {
  return post(source.add, { data: JSON.stringify({ ...formData, tag }) })
}

export function updateSource(formData, tag) {
  return post(source.update, { data: JSON.stringify({ ...formData, tag }) })
}

export function deleteSource(id, tag) {
  return post(source.delete, { data: JSON.stringify({ id, tag }) })
}

export function getSystemList(formData) {
  return post(source.systemList, { data: JSON.stringify(formData) })
}