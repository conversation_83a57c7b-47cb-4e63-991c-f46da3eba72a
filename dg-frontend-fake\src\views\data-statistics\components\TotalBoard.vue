<template>
  <div class="total-board">
    <div class="total-card">
      <el-image
        :src="require('@/assets/images/total-board-icon_tag.svg')"
        style="width: 48px; height: 48px"></el-image>
      <p class="prefix">工单总数</p>
      <p class="count">{{ formatNumberWithComma(data?.ORDER_TOTAL) }}</p>
    </div>
    <div class="total-card">
      <el-image
        :src="require('@/assets/images/total-board-icon_object.svg')"
        style="width: 48px; height: 48px"></el-image>
      <p class="prefix">涉及的诉求对象数</p>
      <p class="count">{{ formatNumberWithComma(data?.APPEAL_NAME_TOTAL) }}</p>
    </div>
  </div>
</template>

<script>
import { formatNumberWithComma } from '@/utils/formatter.js'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {}
  },
  computed: {},
  methods: {
    formatNumberWithComma,
  },
}
</script>

<style lang="scss" scoped>
.total-board {
  @include flex-col;
  @include full;

  .total-card {
    @include flex-col;
    align-items: flex-start;
    flex: 1;
    padding: 16px;
    width: 100%;
    border-radius: 4px;

    .prefix {
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      color: $txtColor-light;
    }

    .count {
      font-size: 30px;
      font-weight: 700;
      line-height: 48px;
      color: $txtColor;
      word-break: keep-all;
    }

    &:nth-child(1) {
      background: url('@/assets/images/total-board-bg_tag.svg') no-repeat center/cover;
    }

    &:nth-child(2) {
      background: url('@/assets/images/total-board-bg_object.svg') no-repeat center/cover;
    }

    + .total-card {
      margin-top: 12px;
    }
  }
}
</style>
