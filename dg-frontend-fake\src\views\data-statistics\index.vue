<template>
  <base-card id="data-statistics" class="y-page">
    <!-- <aside-bar
      ref="asideBar"
      fixType="0"
      @set-current="setCurrent($event)"></aside-bar> -->
    <div class="y-container no-padding">
      <div class="main-wrapper y-container--tight no-padding">
        <div class="chart-box">
          <total-board :data="totalData"></total-board>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">诉求对象关联标签分析</h5>
          <relation-chart :data="relationData"></relation-chart>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">诉求对象构成</h5>
          <object-type-chart :data.sync="objectTypeData"></object-type-chart>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">投资者身份构成</h5>
          <investor-chart :data.sync="investorData"></investor-chart>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">工单类型占比</h5>
          <order-type-chart :data="orderTypeData"></order-type-chart>
        </div>
        <div class="chart-box">
          <div class="part y-container no-padding">
            <h5 class="chart-title">各区域标签分析（数量）</h5>
            <area-chart
              :data="areaData"
              @area-change="handleAreaChange"
              :area-map="areaMap"
            ></area-chart>
          </div>
          <div class="sep"></div>
          <div class="part y-container no-padding">
            <h5 class="chart-title">
              {{ areaMap[province] || "全国" }}前十的标签排名
            </h5>
            <tag-ranking :data="tagData ? tagData : relationData"></tag-ranking>
          </div>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">无效工单</h5>
          <invalid-chart :data="invalidData"></invalid-chart>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">敏感工单</h5>
          <sensitive-chart :data="sensitiveData"></sensitive-chart>
        </div>
        <!-- <div class="chart-box">
          <h5 class="chart-title">高频问题分析</h5>
          <high-freq-table></high-freq-table>
        </div> -->
      </div>
    </div>
  </base-card>
</template>

<script>
import {
  getTotal,
  getRelation,
  getObjectType,
  getInvestor,
  getOrderType,
  getArea,
  getInvalid,
  getSensitive,
  getAllStatistics,
} from "@/api/data-statistics";

// import AsideBar from '@/views/data-query/components/AsideBar'

const path = require("path");
const modules = require.context("./components", false, /\.vue$/);
const components = Object.fromEntries(
  modules.keys().map((key) => {
    const module = modules(key);
    const name = path.basename(key, ".vue");
    return [name, module.default];
  })
);

const areaMap = {
  110000: "北京",
  120000: "天津",
  130000: "河北",
  140000: "山西",
  150000: "内蒙古",
  210000: "辽宁",
  220000: "吉林",
  230000: "黑龙江",
  310000: "上海",
  320000: "江苏",
  330000: "浙江",
  340000: "安徽",
  350000: "福建",
  360000: "江西",
  370000: "山东",
  410000: "河南",
  420000: "湖北",
  430000: "湖南",
  440000: "广东",
  450000: "广西",
  460000: "海南",
  500000: "重庆",
  510000: "四川",
  520000: "贵州",
  530000: "云南",
  540000: "西藏",
  610000: "陕西",
  620000: "甘肃",
  630000: "青海",
  640000: "宁夏",
  650000: "新疆",
};

export default {
  name: "DataStatistics",
  components: {
    // AsideBar,
    ...components,
  },
  props: {},
  data() {
    return {
      activeMenu: undefined,
      province: undefined,
      formData: {
        startTime: undefined,
        endTime: undefined,
      },
      totalData: undefined,
      relationData: undefined,
      objectTypeData: undefined,
      investorData: undefined,
      orderTypeData: undefined,
      areaData: undefined,
      tagData: undefined,
      invalidData: undefined,
      sensitiveData: undefined,
      areaMap,
    };
  },
  computed: {
    menuData() {
      try {
        let [ds, table] = this.activeMenu.split("|");
        return {
          tagDsId: ds,
          tagDsTable: table,
        };
      } catch (e) {
        return null;
      }
    },
  },
  watch: {
    activeMenu: {
      handler(menu) {
        if (menu || menu === 0) {
          this.fetchData();
        }
      },
    },
    formData: {
      handler(data) {
        if (data.startTime && data.endTime) {
          this.fetchData();
        }
      },
      deep: true,
    },
  },
  created() {},
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      const [err ,res] = await getAllStatistics()
      console.log('res: ', res);
      
    },

    fetchData() {
      if (
        !this.formData.startTime ||
        !this.formData.endTime ||
        !this.activeMenu
      ) {
        return false;
      }
      const payload = {
        ...this.formData,
        ...this.menuData,
      };

      this.getTotal(payload);
      this.getRelation(payload);
      this.getObjectType(payload);
      this.getInvestor(payload);
      this.getOrderType(payload);
      this.getArea(payload);
      this.getInvalid(payload);
      this.getSensitive(payload);
    },
    async getTotal(payload) {
      const [err, res] = await getTotal(payload);
      if (!err) {
        this.totalData = res.data;
      }
    },
    async getRelation(payload) {
      const [err, res] = await getRelation(payload);
      if (!err) {
        this.relationData = res.data;
      }
    },
    async getObjectType(payload) {
      const [err, res] = await getObjectType(payload);
      if (!err) {
        this.objectTypeData = res.data;
      }
    },
    async getInvestor(payload) {
      const [err, res] = await getInvestor(payload);
      if (!err) {
        this.investorData = res.data;
      }
    },
    async getOrderType(payload) {
      const [err, res] = await getOrderType(payload);
      if (!err) {
        this.orderTypeData = res.data;
      }
    },
    async getArea(payload) {
      const [err, res] = await getArea(payload);
      if (!err) {
        this.areaData = res.data;
        this.handleAreaChange();
      }
    },
    async getInvalid(payload) {
      const [err, res] = await getInvalid(payload);
      if (!err) {
        this.invalidData = res.data;
      }
    },
    async getSensitive(payload) {
      const [err, res] = await getSensitive(payload);
      if (!err) {
        this.sensitiveData = res.data;
      }
    },
    async handleAreaChange(province) {
      if (!province && province !== 0) {
        this.tagData = undefined;
        this.province = undefined;
        return false;
      }
      this.province = province;
      const payload = {
        ...this.formData,
        ...this.menuData,
        province,
      };
      const [err, res] = await getRelation(payload);
      if (!err) {
        this.tagData = res.data;
      }
    },
  },
};
</script>

<style lang="scss">
#data-statistics {
  @include flex-row;
  background-color: transparent;

  > .aside-bar {
    margin-right: 16px;
    border-radius: 4px;
    background-color: $bgColor;
  }

  > .y-container {
    width: calc(100% - 304px);
    min-width: 1100px;
  }

  .header {
    justify-content: flex-start;
    background-color: $bgColor;
    border-radius: 4px;
  }

  .main-wrapper {
    display: grid;
    gap: 16px;
    grid-auto-rows: 388px 500px 600px 464px;
    grid-template-columns: repeat(6, minmax(170px, 1fr));
    grid-template-areas:
      "a b b b b b"
      "c c d d e e"
      "f f f f f f"
      "g g g h h h";
    margin-top: 16px;
    width: 100%;
    overflow: hidden;
    overflow-y: auto;

    > .chart-box {
      @include container;
      padding: 16px 24px 24px 24px;
      border-radius: 4px;
      background-color: $bgColor;

      .chart-title {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: $txtColor;
      }

      &:nth-child(1) {
        grid-area: a;
      }

      &:nth-child(2) {
        grid-area: b;
      }

      &:nth-child(3) {
        grid-area: c;
      }

      &:nth-child(4) {
        grid-area: d;
      }

      &:nth-child(5) {
        grid-area: e;
      }

      &:nth-child(6) {
        @include flex-row;
        grid-area: f;
        position: relative;
        align-items: flex-start;

        .part {
          height: 100%;
        }

        .part:nth-child(1) {
          flex: 3;
        }

        .part:nth-child(3) {
          flex: 2;
        }

        .sep {
          flex: 0 0 1px;
          margin: 0 24px;
          width: 1px;
          height: 100%;
          background-color: $borderColor;
          transform: scaleX(0.5);
        }
      }

      &:nth-child(7) {
        grid-area: g;
      }

      &:nth-child(8) {
        grid-area: h;
      }
    }
  }
}
</style>
