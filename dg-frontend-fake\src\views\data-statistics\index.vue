<template>
  <base-card id="data-statistics" class="y-page">
    <!-- <aside-bar
      ref="asideBar"
      fixType="0"
      @set-current="setCurrent($event)"></aside-bar> -->
    <div class="y-container no-padding">
      <div class="main-wrapper y-container--tight no-padding">
        <div class="chart-box">
          <total-board :data="totalData"></total-board>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">回应情况统计分析</h5>
          <relation-chart :data="relationData"></relation-chart>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">本人身份确认</h5>
          <object-type-chart :data.sync="objectTypeData"></object-type-chart>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">否认情况分布</h5>
          <investor-chart :data.sync="investorData"></investor-chart>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">申请渠道分布</h5>
          <order-type-chart :data="orderTypeData"></order-type-chart>
        </div>
        <div class="chart-box">
          <div class="part y-container no-padding">
            <h5 class="chart-title">通话时间分布（按日期）</h5>
            <area-chart
              :data="areaData"
              @area-change="handleAreaChange"
              :area-map="areaMap"
            ></area-chart>
          </div>
          <div class="sep"></div>
          <div class="part y-container no-padding">
            <h5 class="chart-title">
              通话时段分布排名
            </h5>
            <tag-ranking :data="tagData ? tagData : relationData"></tag-ranking>
          </div>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">拒绝通话统计</h5>
          <invalid-chart :data="invalidData"></invalid-chart>
        </div>
        <div class="chart-box">
          <h5 class="chart-title">投诉威胁统计</h5>
          <sensitive-chart :data="sensitiveData"></sensitive-chart>
        </div>
        <!-- <div class="chart-box">
          <h5 class="chart-title">高频问题分析</h5>
          <high-freq-table></high-freq-table>
        </div> -->
      </div>
    </div>
  </base-card>
</template>

<script>
import {
  getAllStatistics,
} from "@/api/data-statistics";

// import AsideBar from '@/views/data-query/components/AsideBar'

const path = require("path");
const modules = require.context("./components", false, /\.vue$/);
const components = Object.fromEntries(
  modules.keys().map((key) => {
    const module = modules(key);
    const name = path.basename(key, ".vue");
    return [name, module.default];
  })
);

const areaMap = {
  110000: "北京",
  120000: "天津",
  130000: "河北",
  140000: "山西",
  150000: "内蒙古",
  210000: "辽宁",
  220000: "吉林",
  230000: "黑龙江",
  310000: "上海",
  320000: "江苏",
  330000: "浙江",
  340000: "安徽",
  350000: "福建",
  360000: "江西",
  370000: "山东",
  410000: "河南",
  420000: "湖北",
  430000: "湖南",
  440000: "广东",
  450000: "广西",
  460000: "海南",
  500000: "重庆",
  510000: "四川",
  520000: "贵州",
  530000: "云南",
  540000: "西藏",
  610000: "陕西",
  620000: "甘肃",
  630000: "青海",
  640000: "宁夏",
  650000: "新疆",
};

export default {
  name: "DataStatistics",
  components: {
    // AsideBar,
    ...components,
  },
  props: {},
  data() {
    return {
      activeMenu: undefined,
      province: undefined,
      formData: {
        startTime: undefined,
        endTime: undefined,
      },
      totalData: undefined,
      relationData: undefined,
      objectTypeData: undefined,
      investorData: undefined,
      orderTypeData: undefined,
      areaData: undefined,
      tagData: undefined,
      invalidData: undefined,
      sensitiveData: undefined,
      areaMap,
    };
  },
  computed: {
    menuData() {
      try {
        let [ds, table] = this.activeMenu.split("|");
        return {
          tagDsId: ds,
          tagDsTable: table,
        };
      } catch (e) {
        return null;
      }
    },
  },
  watch: {
    activeMenu: {
      handler(menu) {
        if (menu || menu === 0) {
          this.fetchData();
        }
      },
    },
    formData: {
      handler(data) {
        if (data.startTime && data.endTime) {
          this.fetchData();
        }
      },
      deep: true,
    },
  },
  created() {},
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      const [err, res] = await getAllStatistics()
      if (!err && res.data) {
        this.transformData(res.data)
      }
    },

    transformData(data) {
      // 转换总数据看板
      this.totalData = {
        ORDER_TOTAL: data.chatContentStats.totalConversations,
        APPEAL_NAME_TOTAL: Math.round(parseFloat(data.chatContentStats.averageContentLength))
      }

      // 转换回应情况统计
      this.relationData = this.transformResponseStats(data.responseStats)

      // 转换本人身份确认
      this.objectTypeData = this.transformSelfIdentity(data.responseStats.selfIdentity)

      // 转换否认情况分布
      this.investorData = this.transformDenial(data.responseStats.denial)

      // 转换申请渠道分布
      this.orderTypeData = this.transformApplicationChannels(data.channelStats.applicationChannels)

      // 转换时间分布数据
      this.areaData = this.transformTimeByDate(data.timeStats.byDate)

      // 保存原始时段数据并转换
      this.originalTimeByHour = data.timeStats.byHour
      this.tagData = this.transformTimeByHour(data.timeStats.byHour)

      // 转换拒绝通话统计
      this.invalidData = this.transformNoMoreCalls(data.complaintStats.noMoreCalls)

      // 转换投诉威胁统计
      this.sensitiveData = this.transformPotentialComplaints(data.complaintStats.potentialComplaints)
    },

    // 转换回应情况统计数据
    transformResponseStats(responseStats) {
      const result = []

      // 处理各种回应统计
      Object.keys(responseStats).forEach(key => {
        if (key !== 'total' && typeof responseStats[key] === 'object') {
          Object.keys(responseStats[key]).forEach(subKey => {
            if (subKey !== 'total') {
              result.push({
                DG_APPEAL_MOTIF: `${this.getStatLabel(key)}-${subKey}`,
                APPEAL_MOTIF_TOTAL: responseStats[key][subKey]
              })
            }
          })
        }
      })

      return result
    },

    // 获取统计标签的中文名称
    getStatLabel(key) {
      const labelMap = {
        'selfIdentity': '本人身份',
        'denial': '否认情况',
        'willingToPay': '愿意代还',
        'onTimePayment': '按期还款',
        'promisePayment': '承诺还款',
        'provideContact': '提供联系方式'
      }
      return labelMap[key] || key
    },

    // 转换本人身份数据
    transformSelfIdentity(selfIdentity) {
      return Object.keys(selfIdentity)
        .filter(key => key !== 'total')
        .map(key => ({
          name: key,
          value: selfIdentity[key]
        }))
    },

    // 转换否认情况数据
    transformDenial(denial) {
      return Object.keys(denial)
        .filter(key => key !== 'total')
        .map(key => ({
          name: key,
          value: denial[key]
        }))
    },

    // 转换申请渠道数据
    transformApplicationChannels(applicationChannels) {
      return Object.keys(applicationChannels).map(key => ({
        name: key,
        value: applicationChannels[key]
      }))
    },

    // 转换时间分布数据（按日期）
    transformTimeByDate(byDate) {
      return Object.keys(byDate).map(date => ({
        date: this.formatDate(date),
        value: byDate[date],
        province: date // 使用日期作为省份标识
      }))
    },

    // 格式化日期显示
    formatDate(dateStr) {
      if (dateStr.length === 8) {
        const year = dateStr.substring(0, 4)
        const month = dateStr.substring(4, 6)
        const day = dateStr.substring(6, 8)
        return `${year}-${month}-${day}`
      }
      return dateStr
    },

    // 转换时段排名数据
    transformTimeByHour(byHour) {
      return Object.keys(byHour)
        .map(hour => ({
          DG_APPEAL_MOTIF: `${hour.trim()}时`,
          APPEAL_MOTIF_TOTAL: byHour[hour]
        }))
        .sort((a, b) => {
          const hourA = parseInt(a.DG_APPEAL_MOTIF.replace('时', ''))
          const hourB = parseInt(b.DG_APPEAL_MOTIF.replace('时', ''))
          return hourA - hourB
        })
    },

    // 转换拒绝通话统计
    transformNoMoreCalls(noMoreCalls) {
      return [{
        name: '不要再打',
        value: noMoreCalls['不要再打'] || 0
      }]
    },

    // 转换投诉威胁统计
    transformPotentialComplaints(potentialComplaints) {
      return [{
        name: '再打投诉',
        value: potentialComplaints['再打投诉'] || 0
      }]
    },
    handleAreaChange(dateKey) {
      if (!dateKey && dateKey !== 0) {
        this.tagData = this.transformTimeByHour(this.originalTimeByHour || {});
        this.province = undefined;
        return false;
      }
      this.province = dateKey;
      // 在新的数据结构中，我们直接使用时段数据，不需要额外的API调用
      this.tagData = this.transformTimeByHour(this.originalTimeByHour || {});
    },
  },
};
</script>

<style lang="scss">
#data-statistics {
  @include flex-row;
  background-color: transparent;

  > .aside-bar {
    margin-right: 16px;
    border-radius: 4px;
    background-color: $bgColor;
  }

  > .y-container {
    width: calc(100% - 304px);
    min-width: 1100px;
  }

  .header {
    justify-content: flex-start;
    background-color: $bgColor;
    border-radius: 4px;
  }

  .main-wrapper {
    display: grid;
    gap: 16px;
    grid-auto-rows: 388px 500px 600px 464px;
    grid-template-columns: repeat(6, minmax(170px, 1fr));
    grid-template-areas:
      "a b b b b b"
      "c c d d e e"
      "f f f f f f"
      "g g g h h h";
    margin-top: 16px;
    width: 100%;
    overflow: hidden;
    overflow-y: auto;

    > .chart-box {
      @include container;
      padding: 16px 24px 24px 24px;
      border-radius: 4px;
      background-color: $bgColor;

      .chart-title {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: $txtColor;
      }

      &:nth-child(1) {
        grid-area: a;
      }

      &:nth-child(2) {
        grid-area: b;
      }

      &:nth-child(3) {
        grid-area: c;
      }

      &:nth-child(4) {
        grid-area: d;
      }

      &:nth-child(5) {
        grid-area: e;
      }

      &:nth-child(6) {
        @include flex-row;
        grid-area: f;
        position: relative;
        align-items: flex-start;

        .part {
          height: 100%;
        }

        .part:nth-child(1) {
          flex: 3;
        }

        .part:nth-child(3) {
          flex: 2;
        }

        .sep {
          flex: 0 0 1px;
          margin: 0 24px;
          width: 1px;
          height: 100%;
          background-color: $borderColor;
          transform: scaleX(0.5);
        }
      }

      &:nth-child(7) {
        grid-area: g;
      }

      &:nth-child(8) {
        grid-area: h;
      }
    }
  }
}
</style>
