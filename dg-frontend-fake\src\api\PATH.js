// 系统登录
export const auth = {
  login: '/dg-portal/sso?action=Login',
  menu: '/dg-portal/portal?query=Menu&portal=2000',
  user: '/dg-portal/webcall?action=user.getUserInfo',
  captcha: '/dg-portal/captcha',
  sms: '/dg-portal/sso?action=SmsVerifyCheck',
  logout: '/dg-portal/sso?action=Logout',
}

// Excel数据集
export const dataSet = {
  list: '/dg-etl-mgr/webcall?action=excelSource.getExcelSourceList',
  detail: '/dg-etl-mgr/webcall?action=excelSource.excelSourceDetail',
  log: '/dg-etl-mgr/webcall?action=excelSource.getExcelFailList',
  import: '/dg-etl-mgr/servlet/excelSource?action=excelUpload',
  confirmImport: '/dg-etl-mgr/servlet/excelSource?action=doUpload',
  cancelImport: '/dg-etl-mgr/servlet/excelSource?action=cancleUpload',
  delete: '/dg-etl-mgr/servlet/excelSource?action=delExcelSource',
}

// 关系/目标数据源
export const source = {
  relationList: '/dg-etl-mgr/webcall?action=datasource.getSrcDsList',
  targetList: '/dg-etl-mgr/webcall?action=datasource.getTagDsList',
  tableList: '/dg-etl-mgr/webcall?action=etlDs.getSysDsTableList',
  test: '/dg-etl-mgr/servlet/dataSource?action=dbTest',
  add: '/dg-etl-mgr/servlet/dataSource?action=operateDs',
  update: '/dg-etl-mgr/servlet/dataSource?action=operateDs',
  delete: '/dg-etl-mgr/servlet/dataSource?action=deleteSrcDs',
  systemList: '/dg-etl-mgr/webcall?action=datasource.getSysDsList',
}

// 自助etl
export const etl = {
  etlList: '/dg-etl-mgr/webcall?action=etlFlow.etlFlowPage',
  update: '/dg-etl-mgr/servlet/etlFlow?action=editFlowStatus',
  delete: '/dg-etl-mgr/servlet/etlFlow?action=deleteFlow',
  etlVerList: '/dg-etl-mgr/webcall?action=etlFlow.etlFlowHisPage',
}

// etl日志
export const etlLog = {
  taskList: '/dg-portal/webcall?action=etlTaskDao.etlTaskLogList',
  executeList: '/dg-portal/webcall?action=etlTaskDao.etlTaskExecuteList',
  export: '/dg-portal/servlet/etlTask?action=ExportEtlTaskExecute',
  dict: '/dg-portal/webcall?action=common.etlDict',
  detailHead: '/dg-portal/webcall?action=etlTaskDao.etlLogColumn',
  detail: '/dg-portal/webcall?action=etlTaskDao.etlLogRecord',
}

// 诉求对象管理
export const objectManage = {
  // 分类
  classList: '/dg-portal/webcall?action=appeal.appealTypeDict',
  addClass: '/dg-portal/servlet/appeal?action=addType',
  updateClass: '/dg-portal/servlet/appeal?action=updateType',
  deleteClass: '/dg-portal/servlet/appeal?action=deleteType',
  // 诉求对象
  objectList: '/dg-portal/webcall?action=appeal.getAppealListByType',
  objectDetail: '/dg-portal/webcall?action=appeal.getAppealInfoById',
  importObject: '/dg-portal/servlet/appeal?action=import',
  addObject: '/dg-portal/servlet/appeal?action=add',
  updateObject: '/dg-portal/servlet/appeal?action=update',
  deleteObject: '/dg-portal/servlet/appeal?action=delete',
}

// 投资者句式模板
export const patternTemplate = {
  list: '/dg-portal/webcall?action=dataGovernance.investorConfigList',
  detail: '/dg-portal/webcall?action=dataGovernance.investorConfigById',
  add: '/dg-portal/servlet/dataGovernance?action=InvestorAdd',
  update: '/dg-portal/servlet/dataGovernance?action=InvestorUpdate',
  delete: '/dg-portal/servlet/dataGovernance?action=InvestorDelete',
}

// 行政处罚决定书
export const penaltyDecision = {
  list: '/dg-portal/webcall?action=validateOrderDao.punishInfoList',
}

// 无效工单配置
export const invalidConig = {
  // 分类
  classList: '/dg-portal/webcall?action=validateOrderDao.invalidTypeList',
  addClass: '/dg-portal/servlet/validateOrder?action=InvalidTypeAdd',
  updateClass: '/dg-portal/servlet/validateOrder?action=InvalidTypeUpdate',
  deleteClass: '/dg-portal/servlet/validateOrder?action=InvalidTypeDelete',
  // 答复样例
  answerList: '/dg-portal/webcall?action=validateOrderDao.invalidAnswerList',
  addAnswer: '/dg-portal/servlet/validateOrder?action=InvalidAnswerAdd',
  updateAnswer: '/dg-portal/servlet/validateOrder?action=InvalidAnswerUpdate',
  deleteAnswer: '/dg-portal/servlet/validateOrder?action=InvalidAnswerDelete',
}

// 证监会组织信息
export const csrcInfo = {
  // 组织架构信息
  classTree: '/dg-portal/webcall?action=informantDao.orgTree',
  classList: '/dg-portal/webcall?action=informantDao.queryOrgListByPId',
  addClass: '/dg-portal/servlet/informant?action=OrgAdd',
  updateClass: '/dg-portal/servlet/informant?action=OrgUpdate',
  deleteClass: '/dg-portal/servlet/informant?action=OrgDelete',
  // 人员信息
  staffList: '/dg-portal/webcall?action=informantDao.informantList',
  importStaff: '/dg-portal/servlet/informant?action=Import',
  addStaff: '/dg-portal/servlet/informant?action=Add',
  updateStaff: '/dg-portal/servlet/informant?action=Update',
  deleteStaff: '/dg-portal/servlet/informant?action=Delete',
}

// 数据处理组件
export const processModule = {
  // 分类
  classTree: '/dg-portal/webcall?action=dataGovernance.unitTree',
  addClass: '/dg-portal/servlet/dataGovernance?action=typeAdd',
  updateClass: '/dg-portal/servlet/dataGovernance?action=typeUpdate',
  deleteClass: '/dg-portal/servlet/dataGovernance?action=typeDelete',
  // 组件
  list: '/dg-portal/webcall?action=dataGovernance.dgDataComListByTypeId',
  // list: '/dg-portal/webcall?action=dataGovernance.dgDataComList', // 未分类的
  detail: '/dg-portal/webcall?action=dataGovernance.dgDataComById',
  addModule: '/dg-portal/servlet/dataGovernance?action=DataComAdd',
  updateModule: '/dg-portal/servlet/dataGovernance?action=DataComUpdate',
  deleteModule: '/dg-portal/servlet/dataGovernance?action=DataComDelete',
  serviceDict: '/dg-portal/webcall?action=dataGovernance.getCallBackTaskObjType',
}

// 热词管理
export const hotwordManage = {
  // 分类
  classTree: '/dg-portal/webcall?action=hotWords.unitTree',
  addClass: '/dg-portal/servlet/hotWords?action=typeAdd',
  updateClass: '/dg-portal/servlet/hotWords?action=typeUpdate',
  deleteClass: '/dg-portal/servlet/hotWords?action=typeDelete',
  // 热词
  list: '/dg-portal/webcall?action=hotWords.list',
  addHotword: '/dg-portal/servlet/hotWords?action=add',
  updateHotword: '/dg-portal/servlet/hotWords?action=update',
  deleteHotword: '/dg-portal/servlet/hotWords?action=batchDel',
  importHotword: '/dg-portal/servlet/hotWords?action=import',
  exportHotword: '/dg-portal/servlet/hotWords?action=export',
  generateModel: '/dg-portal/servlet/hotWords?action=pushWord',
}

// 高发事件预警管理
export const highIncidenceManage = {
  list: '/dg-portal/webcall?action=hotWords.getWarnLevelListByTypeId',
  update: '/dg-portal/servlet/hotWords?action=updateWarn',
}

// 数据训练
export const dataTraining = {
  etlList: '/dg-etl-mgr/webcall?action=etlFlow.etlFlowList',
  trainingList: '/dg-portal/webcall?action=dataTraining.dataTrainingList',
  addTraining: '/dg-portal/servlet/dataTraining?action=DataTrainingAdd',
  train: '/dg-portal/servlet/dataTraining?action=TrainEtl',
  queryList: '/dg-portal/webcall?action=dataTraining.dataTrainingAddQueryColumn',
  columnList: '/dg-portal/webcall?action=dataTraining.dataTrainingAddColumn',
  dataList: '/dg-portal/webcall?action=dataTraining.dataTrainingAddList',
  markListHeaders: '/dg-portal/webcall?action=dataTraining.dataTrainingDimensionColumn',
  markList: '/dg-portal/webcall?action=dataTraining.dataTrainingDimensionList',
  markDetail: '/dg-portal/webcall?action=dataTraining.dataTrainingDimensionRecord',
  mark: '/dg-portal/servlet/dataTraining?action=MarkDataTraining',
  historyList: '/dg-portal/webcall?action=dataTraining.dataTrainingDimensionOldList',
}

// 定时任务
export const crontab = {
  list: '/dg-portal/webcall?action=crawletConfig.crawletConfigList',
  detail: '/dg-portal/webcall?action=crawletConfig.crawletConfigById',
  add: '/dg-portal/servlet/crawletConfig?action=CrawletConfigAdd',
  update: '/dg-portal/servlet/crawletConfig?action=CrawletConfigUpdate',
  delete: '/dg-portal/servlet/crawletConfig?action=CrawletConfigDelete',
  execute: '/dg-portal/servlet/crawletConfig?action=CrawletOneClickExec',
}

// 数据查询
export const dataQuery = {
  taskList: '/dg-portal/webcall?action=common.etlDict',
  targetList: '/dg-portal/webcall?action=dataStat.tagDsDict',
  tagQueryList: '/dg-portal/webcall?action=dataStat.getDsQueryWhere',
  etlQueryList: '/dg-portal/webcall?action=dataStat.getQueryWhere',
  tagHeadList: '/dg-portal/webcall?action=dataStat.getDsShow',
  etlHeadList: '/dg-portal/webcall?action=dataStat.getQueryShow',
  tagDataList: '/dg-portal/webcall?action=dataStat.dataStatListForSource',
  etlDataList: '/dg-portal/webcall?action=dataStat.dataStatList',
  tagDict: '/dg-portal/webcall?action=dataStat.getDsTableShow',
  etlDict: '/dg-portal/webcall?action=dataStat.getTableShow',
  tagDetail: '/dg-portal/webcall?action=dataStat.dataStatRecordForSource',
  etlDetail: '/dg-portal/webcall?action=dataStat.dataStatRecord',
  export: '/dg-portal/servlet/dataStat?action=ExportDataStat', // 旧导出接口
  exportByTarget: '/dg-portal/servlet/targetSource?action=ExportTargetSourceExecute',
  exportByEtl: '/dg-portal/servlet/dataStat?action=ExportSelfServiceEtlExecute',
}

// 数据标签统计
export const dataStatistics = {
  total: '/dg-portal/webcall?action=dataLabelStatDao.appealNameTotal',
  relation: '/dg-portal/webcall?action=dataLabelStatDao.appealMotifList',
  objectType: '/dg-portal/webcall?action=dataLabelStatDao.appealTypeList',
  investor: '/dg-portal/webcall?action=dataLabelStatDao.investorList',
  orderType: '/dg-portal/webcall?action=dataLabelStatDao.businessCodeList',
  area: '/dg-portal/webcall?action=dataLabelStatDao.provinceList',
  invalid: '/dg-portal/webcall?action=dataLabelStatDao.investorOrderList',
  sensitive: '/dg-portal/webcall?action=dataLabelStatDao.sensitiveList',

  statistics: '/dg-portal/servlet/model?action=statistics'
}

// 人员管理
export const userManage = {
  list: '/dg-portal/webcall?action=user.userList',
  detail: '/dg-portal/webcall?action=user.getObject',
  import: '/dg-portal/servlet/user?action=BatchSave',
  add: '/dg-portal/servlet/user?action=Save',
  update: '/dg-portal/servlet/user?action=UpdateUserInfo',
  delete: '/dg-portal/servlet/user?action=UserDelete',
  updatePwd: '/dg-portal/servlet/user?action=ModPwd',
  resetPwd: '/dg-portal/servlet/user?action=UserResetPassword',
}

// 角色权限
export const role = {
  list: '/dg-portal/webcall?action=role.roleList',
  add: '/dg-portal/servlet/role?action=AddRole',
  update: '/dg-portal/servlet/role?action=ModRole',
  delete: '/dg-portal/servlet/role?action=DelRole',
  accreditList: '/dg-portal/webcall?action=role.roleUserList',
  accreditableList: '/dg-portal/webcall?action=role.resUserPage',
  accredit: '/dg-portal/servlet/role?action=AddRoleUser',
  disaccredit: '/dg-portal/servlet/role?action=RemoveRoleUser',
  getResTree: '/dg-portal/webcall?action=role.roleResTree',
  updateResTree: '/dg-portal/servlet/role?action=updateRoleRes',
  dict: '/dg-portal/webcall?action=role.roleDictList',
}

// 数据字典
export const dictConfig = {
  dictList: '/dg-portal/webcall?action=dictDao.dictGroupList',
  dictDetail: '/dg-portal/webcall?action=dictDao.dictGroupRecord',
  dictItemList: '/dg-portal/webcall?action=dictDao.dictList',
  dictItemDetail: '/dg-portal/webcall?action=dictDao.dictRecord',
  addDict: '/dg-portal/servlet/dict?action=DictGroupAdd',
  updateDict: '/dg-portal/servlet/dict?action=DictGroupUpdate',
  deleteDict: '/dg-portal/servlet/dict?action=DictGroupDelete',
  addDictItem: '/dg-portal/servlet/dict?action=DictAdd',
  updateDictItem: '/dg-portal/servlet/dict?action=DictUpdate',
  deleteDictItem: '/dg-portal/servlet/dict?action=DictDelete',
  syncRedis: '/dg-portal/servlet/dict?action=SyncRedis',
}

// 导出记录
export const exportRecord = {
  list: '/dg-portal/webcall?action=etlTaskDao.EltExportProgress',
  download: '/dg-portal/servlet/etlTask?action=DownloadEtlTaskExecute',
  delete: '/dg-portal/servlet/etlTask?action=DeleteDownloadFile',
}

export const userProfile = {
  list: '/dg-portal/webcall?action=customer.getCustomerProfileList'
}

export const userBehavior = {
  list: '/dg-portal/webcall?action=customer.getCustomerBehaviorList'
} 